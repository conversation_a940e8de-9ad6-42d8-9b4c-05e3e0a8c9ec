name: neuroworld
description: "A new Flutter project."
publish_to: "none"

# Update version and build number with cider
version: 0.2.1+4

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter

  # state management - riverpod
  hooks_riverpod: ^2.6.1
  flutter_hooks:
  riverpod_annotation: ^2.6.1
  flutter_riverpod: ^2.6.1

  # routing - go_router
  go_router: ^15.1.2

  # data layer - dio + freezed
  dio: ^5.7.0
  http: ^1.3.0
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  fast_immutable_collections: ^11.0.3
  collection: ^1.19.0

  # logging - talker
  talker_flutter: ^4.5.5
  talker_riverpod_logger: ^4.5.5
  talker_dio_logger: ^4.5.5

  #  firebase + auth
  firebase_core: ^3.10.0
  google_sign_in: ^6.2.2
  sign_in_with_apple: ^6.1.4

  shared_preferences: ^2.3.4

  flutter_localizations:
    sdk: flutter
  intl: any

  # image picker
  image_picker: ^1.1.2
  croppy: ^1.3.6
  flutter_image_compress: ^2.4.0
  cached_network_image: ^3.4.1
  flutter_cache_manager: ^3.4.1

  # notifications
  flutter_timezone: ^4.1.0
  firebase_messaging: ^15.2.5
  flutter_local_notifications: ^19.1.0
  permission_handler: ^12.0.0+1

  formz: ^0.8.0
  connectivity_plus: ^6.1.3
  fluttertoast: ^8.2.10
  flutter_svg: ^2.0.17
  lottie: ^3.3.1
  flutter_launcher_icons: ^0.14.3
  cupertino_calendar_picker: ^2.2.4
  action_slider: ^0.7.0
  shimmer: ^3.0.0
  package_info_plus: ^8.3.0
  super_tooltip: ^2.0.9
  dropdown_button2: ^2.3.9
  timeago: ^3.7.0
  gpt_markdown: ^1.0.18
  in_app_purchase: ^3.1.11
  in_app_purchase_storekit: 0.3.7

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  build_runner:
  flutter_gen_runner:
  custom_lint:
  riverpod_lint: ^2.6.3
  riverpod_generator: ^2.6.3
  freezed: ^3.0.4
  json_serializable: ^6.9.0
  go_router_builder: ^2.7.2

flutter_gen:
  output: lib/core/constants/gen/
  line_length: 80 # Optional (default: 80)

  integrations:
    flutter_svg: true
    lottie: true

  assets:
    outputs:
      directory_path_enabled: true

  colors:
    outputs:
      class_name: AppColors
    inputs:
      - lib/core/config/theme/colors.xml

flutter:
  generate: true # enables l10n generation

  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/badges/
    - assets/lottie/
    - assets/svgs/
    - assets/svgs/auth/
    - assets/svgs/onboarding/
    - assets/svgs/bottom_nav/
    - assets/svgs/menu_drawer/
    - assets/svgs/avatars/
    - assets/svgs/goals/
    - assets/svgs/notification/
    - assets/svgs/chat/
    - assets/svgs/badges/

  fonts:
    - family: SF Pro Text
      fonts:
        - asset: assets/fonts/SFProText-Light.ttf
          weight: 300
        - asset: assets/fonts/SFProText-Regular.ttf
        - asset: assets/fonts/SFProText-Medium.ttf
          weight: 500
        - asset: assets/fonts/SFProText-Semibold.ttf
          weight: 600
        - asset: assets/fonts/SFProText-Bold.ttf
          weight: 700
        - asset: assets/fonts/SFProText-Heavy.ttf
          weight: 900
        - asset: assets/fonts/SFProText-LightItalic.ttf
          weight: 300
          style: italic
        - asset: assets/fonts/SFProText-RegularItalic.ttf
          style: italic
        - asset: assets/fonts/SFProText-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/fonts/SFProText-SemiboldItalic.ttf
          weight: 600
          style: italic
        - asset: assets/fonts/SFProText-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: assets/fonts/SFProText-HeavyItalic.ttf
          weight: 900
          style: italic
