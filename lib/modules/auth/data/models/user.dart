import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/auth/data/models/active_subscription.dart';
import 'package:neuroworld/modules/auth/data/models/profile.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
sealed class User with _$User {
  @JsonSerializable(fieldRename: FieldRename.snake)
  const factory User({
    required String id,
    required String email,
    required bool hasActiveSubscription,
    ActiveSubscription? activeSubscription,
    required Profile profile,
    required IList<SelectedGoal> selectedGoals,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}
