import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/dialogs.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/auth/data/models/user.dart';
import 'package:neuroworld/core/ui/widgets/upgrade_plan_dialog.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/auth/ui/providers/selected_island_provider.dart';
import 'package:neuroworld/modules/auth/ui/widgets/brain_assistant.dart';
import 'package:neuroworld/modules/auth/ui/widgets/onboarding_content_widget.dart';
import 'package:neuroworld/modules/goals/data/island_category_data.dart';
import 'package:neuroworld/modules/goals/data/models/request/get_goal_request.dart';
import 'package:neuroworld/modules/goals/data/models/response/get_goal_response.dart';
import 'package:neuroworld/modules/goals/ui/controllers/goal_controller.dart';
import 'package:neuroworld/modules/goals/ui/providers/view_mode_provider.dart';
import 'package:neuroworld/modules/goals/ui/widgets/island_selection_list.dart';
import 'package:neuroworld/modules/goals/ui/widgets/view_toggle.dart';
import 'package:neuroworld/modules/onboarding/data/onboarding_data.dart';

class AddGoal extends HookConsumerWidget {
  const AddGoal({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final onboardingData = OnboardingData.forStep(2);
    final user = ref.read(authStateProvider)?.user;
    final selectedGoals = user?.selectedGoals ?? const IList.empty();
    final selectedMode = ref.watch(viewModeProvider);

    final goalsAsync = ref.watch(goalControllerProvider);

    final selectedIsland = ref.watch(selectedIslandProvider);

    if (goalsAsync.value == null && !goalsAsync.isLoading) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(goalControllerProvider.notifier).getGoals(
              request: const GetGoalRequest(all: true),
            );
      });
    }

    GetGoalsResponse? goalsResponse = goalsAsync.value;

    final allIslandCategories = getIslandCategories(context);

    final (allGoals, disabledIslands) = _filterIslandsAndGoals(
        allIslandCategories, goalsResponse, user, selectedGoals);
    useEffect(() {
      // TODO: && user has free tier
      if (selectedGoals.length >= 5) {
        Future.microtask(() {
          if (context.mounted) {
            Dialogs.showCustomDialog(context, content: UpgradePlanDialog());
          }
        });
      }
      return null;
    }, []);

    ref.easyListen(
      goalControllerProvider,
      loadingText: context.L.gettingGoal,
      whenData: (state) {
        if (state != null && selectedIsland != null) {
          context.push(
            OnboardingAddGoalRoute().location,
            extra: selectedIsland,
          );
        }
      },
    );

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        leading: IconButton(
          icon: Assets.svgs.backArrow.svg(),
          onPressed: () {
            context.pop();
          },
        ),
        title: Text(
          context.L.selectIsland,
          style: TextStyles.subheading1.copyWith(color: AppColors.textPrimary),
        ),
        bottom: const PreferredSize(
          preferredSize: Size.fromHeight(1),
          child: Divider(height: 1, color: AppColors.stroke),
        ),
      ),
      body: goalsAsync.isLoading
          ? const Center(child: CircularProgressIndicator())
          : Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(Assets.images.bg.path),
                  fit: BoxFit.cover,
                ),
              ),
              child: Column(
                children: [
                  const Padding(
                    padding: EdgeInsets.fromLTRB(24, 16, 24, 0),
                    child: ViewToggle(),
                  ),
                  Expanded(
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 250),
                      child: selectedMode == ViewModeEnum.map
                          ? SingleChildScrollView(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 0),
                              child: OnboardingContent(
                                onboardingData: onboardingData,
                                isAddGoal: true,
                                disabledIslands: disabledIslands,
                                allGoals: allGoals,
                              ),
                            )
                          : IslandSelectionList(
                              disabledKeys: disabledIslands,
                              allGoals: allGoals,
                            ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(color: AppColors.stroke),
                    width: double.infinity,
                    height: 1.5,
                  ),
                  const SizedBox(height: 16),
                  BrainAssistant(
                    buttonTitle: context.L.askMe,
                    message: context.L.letMeKnow,
                    avatarPath: Assets.svgs.onboarding.brainAssistant.path,
                    avatarPosition: AvatarPosition.leftCenter,
                    avatarSize: 86,
                    onTap: () {
                      ChatRoute().push(context);
                    },
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(30, 24, 30, 64),
                    child: Row(
                      children: [
                        const SizedBox(width: 10),
                        Expanded(
                          child: PrimaryButton(
                            onPressed: () {
                              if (selectedIsland != null) {
                                ref
                                    .read(goalControllerProvider.notifier)
                                    .getGoals();
                              }
                            },
                            disabled: selectedIsland == null ||
                                _isButtonDisabled(
                                    user, selectedGoals, allGoals),
                            child: Text(onboardingData.buttonText(context)),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  bool _isButtonDisabled(
      User? user, IList<dynamic> selectedGoals, List<dynamic> allGoals) {
    if (user?.hasActiveSubscription == true) {
      return false;
    }
    return selectedGoals.length == allGoals.length;
  }

  (List<IslandCategory>, List<String>) _filterIslandsAndGoals(
    List<IslandCategory> allIslandCategories,
    GetGoalsResponse? goalsResponse,
    User? user,
    IList<dynamic> selectedGoals,
  ) {
    if (goalsResponse == null) {
      return (allIslandCategories, <String>[]);
    }

    final goalsByCategory = <String, List<dynamic>>{};
    for (final goal in goalsResponse.goals) {
      final categoryName = goal.category.toString().split('.').last;
      goalsByCategory[categoryName] = goalsByCategory[categoryName] ?? [];
      goalsByCategory[categoryName]!.add(goal);
    }

    final selectedGoalsByCategory = <String, List<dynamic>>{};
    for (final selectedGoal in selectedGoals) {
      final categoryName =
          selectedGoal.goal.category.toString().split('.').last;
      selectedGoalsByCategory[categoryName] =
          selectedGoalsByCategory[categoryName] ?? [];
      selectedGoalsByCategory[categoryName]!.add(selectedGoal);
    }

    List<String> disabledIslands = [];

    if (user?.hasActiveSubscription == true) {
      for (final island in allIslandCategories) {
        final categoryName = island.key;
        final availableGoalsInCategory = goalsByCategory[categoryName] ?? [];
        final selectedGoalsInCategory =
            selectedGoalsByCategory[categoryName] ?? [];

        // Disable if no goals available OR all goals in category are selected
        if (availableGoalsInCategory.isEmpty ||
            selectedGoalsInCategory.length >= availableGoalsInCategory.length) {
          disabledIslands.add(categoryName);
        }
      }
    } else {
      for (final island in allIslandCategories) {
        final categoryName = island.key;
        final availableGoalsInCategory = goalsByCategory[categoryName] ?? [];
        final selectedGoalsInCategory =
            selectedGoalsByCategory[categoryName] ?? [];

        if (availableGoalsInCategory.isEmpty ||
            selectedGoalsInCategory.isNotEmpty) {
          disabledIslands.add(categoryName);
        }
      }
    }

    return (allIslandCategories, disabledIslands);
  }
}
