import 'dart:math';

import 'package:neuroworld/modules/auth/data/models/active_subscription.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscription_controller.g.dart';

@riverpod
class SubscriptionController extends _$SubscriptionController {
  @override
  FutureOr<bool?> build() => null;

  /// Updates the user's subscription status after successful purchase
  Future<void> updateSubscriptionStatus(bool hasActiveSubscription,
      {String? productId}) async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(() async {
      // Get current user
      final currentUser = ref.read(authStateProvider)?.user;
      if (currentUser == null) {
        throw Exception('No user found');
      }

      // Create ActiveSubscription object if we have a product ID
      ActiveSubscription? activeSubscription;
      if (hasActiveSubscription && productId != null) {
        activeSubscription = _createActiveSubscriptionFromProductId(productId);
      }

      // Update the user's hasActiveSubscription status and activeSubscription locally first
      // This ensures immediate UI update
      final updatedUser = currentUser.copyWith(
        hasActiveSubscription: hasActiveSubscription,
        activeSubscription: activeSubscription,
      );

      // Update the auth state with the new user data
      ref.read(authStateProvider.notifier).updateUser(updatedUser);

      // Refresh user data from server to get the latest subscription info
      // This ensures we have the most up-to-date activeSubscription object
      final authService = ref.read(authServiceProvider);
      final refreshedAuth = await authService.refreshUser();
      ref.read(authStateProvider.notifier).login(refreshedAuth);

      return hasActiveSubscription;
    });
  }

  /// Refreshes subscription status from server
  Future<void> refreshSubscriptionStatus() async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      final refreshedAuth = await authService.refreshUser();
      ref.read(authStateProvider.notifier).login(refreshedAuth);

      return refreshedAuth.user.hasActiveSubscription;
    });
  }

  /// Creates an ActiveSubscription object from a product ID
  ActiveSubscription _createActiveSubscriptionFromProductId(String productId) {
    final now = DateTime.now();
    final subscriptionInfo = _parseProductId(productId);

    // Generate random IDs (in a real app, these would come from the server)
    final subscriptionId = _generateRandomId();
    final planId = _generateRandomId();

    // Calculate expiry time based on billing period
    final expiryTime = subscriptionInfo.billingPeriod == 'monthly'
        ? now.add(const Duration(days: 30))
        : now.add(const Duration(days: 365));

    final plan = Plan(
      id: planId,
      tier: subscriptionInfo.tier,
      name: subscriptionInfo.name,
      billingPeriod: subscriptionInfo.billingPeriod,
      productId: productId,
      durationDays: subscriptionInfo.durationDays,
    );

    return ActiveSubscription(
      id: subscriptionId,
      plan: plan,
      status: 'active',
      startTime: now,
      expiryTime: expiryTime,
    );
  }

  /// Parses product ID to extract subscription information
  ({String tier, String name, String billingPeriod, int durationDays})
      _parseProductId(String productId) {
    // Product ID format: com.neuroworld.dev.{tier}.{period}
    // Examples:
    // - com.neuroworld.dev.habit.monthly
    // - com.neuroworld.dev.habit.yearly
    // - com.neuroworld.dev.summit.monthly
    // - com.neuroworld.dev.clinical.yearly

    final parts = productId.split('.');
    if (parts.length < 6) {
      throw Exception('Invalid product ID format: $productId');
    }

    final tier = parts[4]; // habit, summit, clinical, academy
    final period = parts[5]; // monthly, yearly

    // Map academy to habits (based on the product IDs in purchase_service.dart)
    final normalizedTier = tier == 'academy' ? 'habits' : tier;

    // Create human-readable name
    final tierName = _capitalizeTier(normalizedTier);
    final periodName = period == 'monthly' ? 'Monthly' : 'Yearly';
    final name = '$tierName $periodName';

    // Calculate duration days
    final durationDays = period == 'monthly' ? 30 : 365;

    return (
      tier: normalizedTier,
      name: name,
      billingPeriod: period,
      durationDays: durationDays,
    );
  }

  /// Capitalizes tier name for display
  String _capitalizeTier(String tier) {
    switch (tier.toLowerCase()) {
      case 'habits':
        return 'Habits';
      case 'summit':
        return 'Summit';
      case 'clinical':
        return 'Clinical';
      default:
        return tier[0].toUpperCase() + tier.substring(1);
    }
  }

  /// Generates a random ID (UUID-like)
  String _generateRandomId() {
    final random = Random();
    const chars = 'abcdef0123456789';

    String generateSegment(int length) {
      return List.generate(
          length, (index) => chars[random.nextInt(chars.length)]).join();
    }

    return '${generateSegment(8)}-${generateSegment(4)}-${generateSegment(4)}-${generateSegment(4)}-${generateSegment(12)}';
  }
}
