import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscription_controller.g.dart';

@riverpod
class SubscriptionController extends _$SubscriptionController {
  @override
  FutureOr<bool?> build() => null;

  /// Updates the user's subscription status after successful purchase
  Future<void> updateSubscriptionStatus(bool hasActiveSubscription) async {
    state = const AsyncLoading();
    
    state = await AsyncValue.guard(() async {
      // Get current user
      final currentUser = ref.read(authStateProvider)?.user;
      if (currentUser == null) {
        throw Exception('No user found');
      }

      // Update the user's hasActiveSubscription status locally first
      // This ensures immediate UI update
      final updatedUser = currentUser.copyWith(
        hasActiveSubscription: hasActiveSubscription,
      );
      
      // Update the auth state with the new user data
      ref.read(authStateProvider.notifier).updateUser(updatedUser);

      // Refresh user data from server to get the latest subscription info
      // This ensures we have the most up-to-date activeSubscription object
      final authService = ref.read(authServiceProvider);
      final refreshedAuth = await authService.refreshUser();
      ref.read(authStateProvider.notifier).login(refreshedAuth);

      return hasActiveSubscription;
    });
  }

  /// Refreshes subscription status from server
  Future<void> refreshSubscriptionStatus() async {
    state = const AsyncLoading();
    
    state = await AsyncValue.guard(() async {
      final authService = ref.read(authServiceProvider);
      final refreshedAuth = await authService.refreshUser();
      ref.read(authStateProvider.notifier).login(refreshedAuth);
      
      return refreshedAuth.user.hasActiveSubscription;
    });
  }
}
